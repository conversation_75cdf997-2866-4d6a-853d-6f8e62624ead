2025-08-01 11:30:53 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 11:30:53 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 11:30:53 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 11:30:53 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 11:30:53 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-01 11:30:53 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 11:31:02 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 11:31:04 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 11:31:04 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 11:31:04 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-01 11:31:04 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-01 11:31:04 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-01 11:31:04 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-01 11:31:04 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-01 11:31:05 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-01 11:31:05 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-01 11:31:07 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-01 11:31:07 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-01 11:31:07 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-01 11:31:07 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-01 11:31:07 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-01 11:31:08 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-01 11:31:08 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-01 11:31:08 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-01 11:31:08 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-01 11:31:08 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-01 11:31:08 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-01 11:31:09 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-01 11:31:09 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-01 11:31:09 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-01 11:31:09 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-01 11:31:09 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-01 11:31:09 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-01 11:31:12 - [32mINFO[0m - Using existing S3 bucket: cscsrag1[0m
2025-08-01 11:31:12 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-01 11:31:12 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-01 11:31:14 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 11:31:14 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-01 11:31:14 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-01 11:31:14 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-01 11:31:16 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-01 11:31:17 - [32mINFO[0m - Scheduler started[0m
2025-08-01 11:31:17 - [32mINFO[0m - Scheduler started[0m
2025-08-01 11:31:17 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-01 11:31:17 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-01 11:31:17 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 11:31:18 - [32mINFO[0m - Started server process [15196][0m
2025-08-01 11:31:18 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-01 11:31:18 - [32mINFO[0m - Application startup complete.[0m
2025-08-01 11:31:18 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-01 11:31:37 - [32mINFO[0m - 127.0.0.1:62700 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 11:31:38 - [32mINFO[0m - 127.0.0.1:62700 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 11:31:38 - [32mINFO[0m - 127.0.0.1:62700 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 11:31:39 - [32mINFO[0m - 127.0.0.1:62700 - "OPTIONS /v3/users/logout HTTP/1.1" [32m200[0m
2025-08-01 11:31:40 - [32mINFO[0m - 127.0.0.1:62704 - "POST /v3/users/logout HTTP/1.1" [32m200[0m
2025-08-01 11:31:48 - [32mINFO[0m - 127.0.0.1:62705 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-08-01 11:31:48 - [32mINFO[0m - 127.0.0.1:62705 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-08-01 11:31:49 - [32mINFO[0m - 127.0.0.1:62705 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-08-01 11:31:49 - [32mINFO[0m - 127.0.0.1:62705 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-08-01 11:31:50 - [32mINFO[0m - 127.0.0.1:62705 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-08-01 11:31:50 - [32mINFO[0m - 127.0.0.1:62705 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 11:31:51 - [32mINFO[0m - 127.0.0.1:62705 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 11:32:00 - [32mINFO[0m - 127.0.0.1:62802 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 11:32:01 - [32mINFO[0m - 127.0.0.1:62802 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 11:32:33 - [32mINFO[0m - 127.0.0.1:63044 - "OPTIONS /v3/users/2acb499e-8428-543b-bd85-0d9098718220/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-08-01 11:32:33 - [32mINFO[0m - 127.0.0.1:63044 - "OPTIONS /v3/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-08-01 11:32:33 - [32mINFO[0m - 127.0.0.1:63044 - "GET /v3/users/2acb499e-8428-543b-bd85-0d9098718220/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-08-01 11:32:34 - [32mINFO[0m - 127.0.0.1:63048 - "GET /v3/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-08-01 11:36:50 - [32mINFO[0m - 127.0.0.1:64109 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:36:50 - [32mINFO[0m - 127.0.0.1:64109 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:41:51 - [32mINFO[0m - 127.0.0.1:65195 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:46:51 - [32mINFO[0m - 127.0.0.1:51118 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:46:52 - [32mINFO[0m - 127.0.0.1:51118 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:51:53 - [32mINFO[0m - 127.0.0.1:52039 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:56:53 - [32mINFO[0m - 127.0.0.1:52811 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 11:56:54 - [32mINFO[0m - 127.0.0.1:52811 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:01:55 - [32mINFO[0m - 127.0.0.1:53602 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:06:55 - [32mINFO[0m - 127.0.0.1:54487 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:06:56 - [32mINFO[0m - 127.0.0.1:54487 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:11:57 - [32mINFO[0m - 127.0.0.1:55249 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:16:57 - [32mINFO[0m - 127.0.0.1:56023 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:16:58 - [32mINFO[0m - 127.0.0.1:56023 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:21:59 - [32mINFO[0m - 127.0.0.1:56813 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:26:59 - [32mINFO[0m - 127.0.0.1:57740 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:26:59 - [32mINFO[0m - 127.0.0.1:57740 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:32:00 - [32mINFO[0m - 127.0.0.1:58499 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:37:01 - [32mINFO[0m - 127.0.0.1:59355 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:37:01 - [32mINFO[0m - 127.0.0.1:59355 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:42:02 - [32mINFO[0m - 127.0.0.1:60173 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:47:03 - [32mINFO[0m - 127.0.0.1:60963 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:47:03 - [32mINFO[0m - 127.0.0.1:60963 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:52:04 - [32mINFO[0m - 127.0.0.1:61769 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:57:05 - [32mINFO[0m - 127.0.0.1:62575 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 12:57:05 - [32mINFO[0m - 127.0.0.1:62575 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:02:06 - [32mINFO[0m - 127.0.0.1:63415 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:07:07 - [32mINFO[0m - 127.0.0.1:64315 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:07:07 - [32mINFO[0m - 127.0.0.1:64315 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:12:08 - [32mINFO[0m - 127.0.0.1:65084 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:17:09 - [32mINFO[0m - 127.0.0.1:50475 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:17:09 - [32mINFO[0m - 127.0.0.1:50475 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:22:10 - [32mINFO[0m - 127.0.0.1:51542 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:26:19 - [32mINFO[0m - 127.0.0.1:52518 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:26:20 - [32mINFO[0m - 127.0.0.1:52518 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:26:55 - [32mINFO[0m - 127.0.0.1:52525 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-08-01 13:26:56 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-08-01 13:26:56 - [32mINFO[0m - 127.0.0.1:52525 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:26:57 - [31mERROR[0m - Error storing file in S3: Parameter validation failed:
Non ascii characters found in S3 metadata for key "filename", value: "Dify&RAGFlow��¼������ָ��.docx".  
S3 metadata can only contain ASCII characters. [0m
2025-08-01 13:26:57 - [31mERROR[0m - 127.0.0.1:52653 - "POST /v3/documents HTTP/1.1" [31m500[0m
2025-08-01 13:27:10 - [32mINFO[0m - 127.0.0.1:52654 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:27:11 - [32mINFO[0m - 127.0.0.1:52654 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:32:12 - [32mINFO[0m - 127.0.0.1:53930 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:33:22 - [32mINFO[0m - Shutting down[0m
2025-08-01 13:33:22 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-08-01 13:33:22 - [32mINFO[0m - Application shutdown complete.[0m
2025-08-01 13:33:22 - [32mINFO[0m - Finished server process [15196][0m
2025-08-01 13:35:21 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 13:35:21 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 13:35:21 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 13:35:21 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:35:21 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-01 13:35:21 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:35:27 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:35:29 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:35:29 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:35:29 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-01 13:35:29 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-01 13:35:29 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-01 13:35:29 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-01 13:35:29 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-01 13:35:30 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-01 13:35:30 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: system[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-01 13:35:32 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-01 13:35:33 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-01 13:35:33 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-01 13:35:33 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-01 13:35:33 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-01 13:35:33 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-01 13:35:33 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-01 13:35:34 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-01 13:35:34 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-01 13:35:34 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-01 13:35:34 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-01 13:35:34 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-01 13:35:34 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-01 13:35:37 - [32mINFO[0m - Using existing S3 bucket: cscsrag1[0m
2025-08-01 13:35:37 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-01 13:35:37 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-01 13:35:39 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 13:35:39 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-01 13:35:39 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-01 13:35:39 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-01 13:35:39 - [32mINFO[0m - Default admin user already exists.[0m
2025-08-01 13:35:39 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-01 13:35:39 - [32mINFO[0m - Scheduler started[0m
2025-08-01 13:35:39 - [32mINFO[0m - Scheduler started[0m
2025-08-01 13:35:39 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-01 13:35:39 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-01 13:35:39 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-01 13:35:39 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-01 13:35:40 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 13:35:41 - [32mINFO[0m - Started server process [24504][0m
2025-08-01 13:35:41 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-01 13:35:41 - [32mINFO[0m - Application startup complete.[0m
2025-08-01 13:35:41 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-01 13:36:04 - [32mINFO[0m - 127.0.0.1:54678 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:36:28 - [32mINFO[0m - 127.0.0.1:54681 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:36:28 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-08-01 13:36:29 - [31mERROR[0m - Error storing file in S3: Parameter validation failed:
Non ascii characters found in S3 metadata for key "filename", value: "03.21.QAT���ȶ���Ѳ������淶˵��.pdf".  
S3 metadata can only contain ASCII characters. [0m
2025-08-01 13:36:29 - [31mERROR[0m - 127.0.0.1:54681 - "POST /v3/documents HTTP/1.1" [31m500[0m
2025-08-01 13:36:29 - [32mINFO[0m - 127.0.0.1:54776 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:41:04 - [32mINFO[0m - 127.0.0.1:55653 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:41:04 - [32mINFO[0m - 127.0.0.1:55653 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 13:43:51 - [32mINFO[0m - Shutting down[0m
2025-08-01 13:43:51 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-08-01 13:43:51 - [32mINFO[0m - Application shutdown complete.[0m
2025-08-01 13:43:51 - [32mINFO[0m - Finished server process [24504][0m
2025-08-01 13:46:16 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 13:46:16 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 13:46:16 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 13:46:16 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:46:16 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-01 13:46:16 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:46:21 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:46:23 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:46:23 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 13:46:23 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-01 13:46:23 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-01 13:46:23 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-01 13:46:23 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-01 13:46:23 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-01 13:46:24 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-01 13:46:25 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: system[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-01 13:46:27 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-01 13:46:28 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-01 13:46:28 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-01 13:46:28 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-01 13:46:28 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-01 13:46:28 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-01 13:46:29 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-01 13:46:29 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-01 13:46:29 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-01 13:46:29 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-01 13:46:29 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-01 13:46:29 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-01 13:46:30 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-01 13:46:32 - [32mINFO[0m - Using existing S3 bucket: cscsrag[0m
2025-08-01 13:46:32 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-01 13:46:32 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-01 13:46:34 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 13:46:34 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-01 13:46:34 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-01 13:46:34 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-01 13:46:34 - [32mINFO[0m - Default admin user already exists.[0m
2025-08-01 13:46:34 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-01 13:46:34 - [32mINFO[0m - Scheduler started[0m
2025-08-01 13:46:34 - [32mINFO[0m - Scheduler started[0m
2025-08-01 13:46:34 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-01 13:46:34 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-01 13:46:34 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-01 13:46:35 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-01 13:46:36 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 13:46:36 - [32mINFO[0m - Started server process [21824][0m
2025-08-01 13:46:36 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-01 13:46:36 - [32mINFO[0m - Application startup complete.[0m
2025-08-01 13:46:36 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-01 13:46:49 - [32mINFO[0m - 127.0.0.1:57260 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-08-01 13:46:49 - [32mINFO[0m - 127.0.0.1:57260 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-08-01 13:46:50 - [32mINFO[0m - 127.0.0.1:57260 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-08-01 13:46:50 - [32mINFO[0m - 127.0.0.1:57260 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-08-01 13:46:51 - [32mINFO[0m - 127.0.0.1:57260 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-08-01 13:46:51 - [32mINFO[0m - 127.0.0.1:57260 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 13:46:51 - [32mINFO[0m - 127.0.0.1:57262 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 13:46:52 - [32mINFO[0m - 127.0.0.1:57262 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 13:46:53 - [32mINFO[0m - 127.0.0.1:57262 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 13:46:55 - [32mINFO[0m - 127.0.0.1:57262 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:46:56 - [32mINFO[0m - 127.0.0.1:57262 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:47:14 - [32mINFO[0m - 127.0.0.1:57472 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-08-01 13:47:14 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-08-01 13:47:15 - [32mINFO[0m - 127.0.0.1:57473 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 13:47:15 - [31mERROR[0m - Error storing file in S3: Parameter validation failed:
Non ascii characters found in S3 metadata for key "filename", value: "��ְ�����ݽ���.pdf".  
S3 metadata can only contain ASCII characters. [0m
2025-08-01 13:47:15 - [31mERROR[0m - 127.0.0.1:57472 - "POST /v3/documents HTTP/1.1" [31m500[0m
2025-08-01 13:47:47 - [32mINFO[0m - Shutting down[0m
2025-08-01 13:47:47 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-08-01 13:47:47 - [32mINFO[0m - Application shutdown complete.[0m
2025-08-01 13:47:47 - [32mINFO[0m - Finished server process [21824][0m
2025-08-01 18:21:08 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 18:21:08 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 18:21:08 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 18:21:08 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:21:08 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-01 18:21:08 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:21:19 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:21:22 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:21:22 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:21:22 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-01 18:21:22 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-01 18:21:22 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-01 18:21:22 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-01 18:21:22 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-01 18:21:24 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-01 18:21:24 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: system[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-01 18:21:26 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-01 18:21:27 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-01 18:21:27 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-01 18:21:27 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-01 18:21:27 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-01 18:21:27 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-01 18:21:27 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-01 18:21:28 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-01 18:21:28 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-01 18:21:28 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-01 18:21:28 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-01 18:21:28 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-01 18:21:31 - [32mINFO[0m - Using existing S3 bucket: cscsrag[0m
2025-08-01 18:21:31 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-01 18:21:31 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-01 18:21:33 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:21:33 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-01 18:21:33 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-01 18:21:33 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-01 18:21:34 - [32mINFO[0m - Default admin user already exists.[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-01 18:21:34 - [32mINFO[0m - Scheduler started[0m
2025-08-01 18:21:34 - [32mINFO[0m - Scheduler started[0m
2025-08-01 18:21:34 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-01 18:21:34 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-01 18:21:34 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-01 18:21:35 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 18:21:35 - [32mINFO[0m - Started server process [6632][0m
2025-08-01 18:21:35 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-01 18:21:35 - [32mINFO[0m - Application startup complete.[0m
2025-08-01 18:21:35 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-01 18:22:32 - [32mINFO[0m - 127.0.0.1:61895 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-08-01 18:22:32 - [32mINFO[0m - 127.0.0.1:61895 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-08-01 18:22:33 - [32mINFO[0m - 127.0.0.1:61895 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-08-01 18:22:33 - [32mINFO[0m - 127.0.0.1:61895 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-08-01 18:22:34 - [32mINFO[0m - 127.0.0.1:61895 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-08-01 18:22:39 - [32mINFO[0m - 127.0.0.1:61900 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:22:39 - [32mINFO[0m - 127.0.0.1:61895 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:22:40 - [32mINFO[0m - 127.0.0.1:61895 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:22:40 - [32mINFO[0m - 127.0.0.1:61895 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:22:49 - [32mINFO[0m - 127.0.0.1:61979 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 18:22:50 - [32mINFO[0m - 127.0.0.1:61979 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 18:23:16 - [32mINFO[0m - 127.0.0.1:61985 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-08-01 18:23:17 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-08-01 18:23:17 - [32mINFO[0m - 127.0.0.1:61985 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 18:23:18 - [31mERROR[0m - Error storing file in S3: Parameter validation failed:
Non ascii characters found in S3 metadata for key "filename", value: "��ְ�����ݽ���.pdf".  
S3 metadata can only contain ASCII characters. [0m
2025-08-01 18:23:18 - [31mERROR[0m - 127.0.0.1:62079 - "POST /v3/documents HTTP/1.1" [31m500[0m
2025-08-01 18:27:35 - [32mINFO[0m - 127.0.0.1:63226 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:27:35 - [32mINFO[0m - 127.0.0.1:63226 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:28:44 - [32mINFO[0m - Shutting down[0m
2025-08-01 18:28:44 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-08-01 18:28:44 - [32mINFO[0m - Application shutdown complete.[0m
2025-08-01 18:28:44 - [32mINFO[0m - Finished server process [6632][0m
2025-08-01 18:29:44 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 18:29:44 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 18:29:44 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 18:29:44 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:29:44 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-01 18:29:44 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:29:50 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:29:52 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:29:52 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:29:52 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-01 18:29:52 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-01 18:29:52 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-01 18:29:52 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-01 18:29:52 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-01 18:29:53 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-01 18:29:53 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: system[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-01 18:29:55 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-01 18:29:56 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-01 18:29:56 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-01 18:29:56 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-01 18:29:56 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-01 18:29:56 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-01 18:29:56 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-01 18:29:57 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-01 18:29:58 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-01 18:29:58 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-01 18:30:01 - [32mINFO[0m - Using existing S3 bucket: cscsrag[0m
2025-08-01 18:30:01 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-01 18:30:01 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-01 18:30:04 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:30:04 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-01 18:30:04 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-01 18:30:04 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-01 18:30:05 - [32mINFO[0m - Default admin user already exists.[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-01 18:30:05 - [32mINFO[0m - Scheduler started[0m
2025-08-01 18:30:05 - [32mINFO[0m - Scheduler started[0m
2025-08-01 18:30:05 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-01 18:30:05 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-01 18:30:05 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-01 18:30:06 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 18:30:06 - [32mINFO[0m - Started server process [24228][0m
2025-08-01 18:30:06 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-01 18:30:06 - [32mINFO[0m - Application startup complete.[0m
2025-08-01 18:30:06 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-01 18:37:06 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 18:37:06 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 18:37:06 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-01 18:37:06 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:37:06 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-01 18:37:06 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:37:12 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:37:14 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:37:14 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-01 18:37:14 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-01 18:37:14 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-01 18:37:14 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-01 18:37:14 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-01 18:37:14 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-01 18:37:15 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-01 18:37:16 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: system[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-01 18:37:17 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-01 18:37:18 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-01 18:37:19 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-01 18:37:19 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-01 18:37:19 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-01 18:37:19 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-01 18:37:19 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-01 18:37:19 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-01 18:37:21 - [32mINFO[0m - Using existing S3 bucket: cscsrag[0m
2025-08-01 18:37:21 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-01 18:37:21 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-01 18:37:24 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-01 18:37:24 - [32mINFO[0m - Default admin user already exists.[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-01 18:37:24 - [32mINFO[0m - Scheduler started[0m
2025-08-01 18:37:24 - [32mINFO[0m - Scheduler started[0m
2025-08-01 18:37:24 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-01 18:37:24 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-01 18:37:24 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-01 18:37:25 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-01 18:37:25 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-01 18:37:25 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-01 18:37:25 - [32mINFO[0m - Started server process [27288][0m
2025-08-01 18:37:25 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-01 18:37:25 - [32mINFO[0m - Application startup complete.[0m
2025-08-01 18:37:25 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-01 18:38:10 - [32mINFO[0m - 127.0.0.1:52308 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:38:11 - [32mINFO[0m - 127.0.0.1:52308 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:38:11 - [32mINFO[0m - 127.0.0.1:52308 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-01 18:38:16 - [32mINFO[0m - 127.0.0.1:52308 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 18:38:16 - [32mINFO[0m - 127.0.0.1:52308 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 18:38:34 - [32mINFO[0m - 127.0.0.1:52313 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-08-01 18:38:35 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-08-01 18:38:35 - [32mINFO[0m - 127.0.0.1:52313 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-01 18:38:38 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:38:38 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:38:38 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:38:57 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-08-01 18:38:57 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:38:57 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:38:57 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-08-01 18:38:57 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:38:57 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:38:58 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-08-01 18:38:58 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:38:58 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:38:58 - [31mERROR[0m - Error running orchestrated ingestion: Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`. 

Attempting to run without orchestration.[0m
2025-08-01 18:38:58 - [32mINFO[0m - Running ingestion without orchestration for file ��ְ�����ݽ���.pdf and document_id 55b09cdc-5de0-567a-bf36-dcdd237269fa.[0m
2025-08-01 18:38:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:38:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:38:59 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-01 18:39:18 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-08-01 18:39:18 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:39:18 - [33mWARNING[0m - Request failed (attempt 1): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:39:19 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-08-01 18:39:19 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:39:19 - [33mWARNING[0m - Request failed (attempt 2): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:39:20 - [33mWARNING[0m - Failed to truncate texts: This model isn't mapped yet. model=openai/aliyun/text-embedding-v4, custom_llm_provider=openai. Add it here - https://github.com/BerriAI/litellm/blob/main/model_prices_and_context_window.json.[0m
2025-08-01 18:39:20 - [31mERROR[0m - Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:39:20 - [33mWARNING[0m - Request failed (attempt 3): Error getting embeddings: litellm.UnsupportedParamsError: Setting dimensions is not supported for OpenAI `text-embedding-3` and later models. To drop it from the call, set `litellm.drop_params = True`.[0m
2025-08-01 18:39:20 - [33mWARNING[0m - 127.0.0.1:52408 - "POST /v3/documents HTTP/1.1" [33m400[0m
2025-08-01 18:43:10 - [32mINFO[0m - 127.0.0.1:53093 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:43:10 - [32mINFO[0m - 127.0.0.1:53093 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:48:11 - [32mINFO[0m - 127.0.0.1:53824 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:53:12 - [32mINFO[0m - 127.0.0.1:54405 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:53:12 - [32mINFO[0m - 127.0.0.1:54405 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:58:13 - [32mINFO[0m - 127.0.0.1:54798 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-01 18:58:59 - [32mINFO[0m - Shutting down[0m
2025-08-01 18:58:59 - [32mINFO[0m - Waiting for application shutdown.[0m
2025-08-01 18:58:59 - [32mINFO[0m - Application shutdown complete.[0m
2025-08-01 18:58:59 - [32mINFO[0m - Finished server process [27288][0m
2025-08-04 08:45:40 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-04 08:45:40 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=nan rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-04 08:45:40 - [33mWARNING[0m - Aliyun embedding model detected, dropping params[0m
2025-08-04 08:45:40 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=nan rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-04 08:45:40 - [33mWARNING[0m - Aliyun embedding model detected, dropping params[0m
2025-08-04 08:45:40 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:45:40 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-04 08:45:40 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:45:53 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:45:56 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:45:56 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:45:56 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-04 08:45:56 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-04 08:45:56 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-04 08:45:56 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-04 08:45:56 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-04 08:46:18 - [31mERROR[0m - Error Error [WinError 121] �źŵƳ�ʱʱ���ѵ� occurred while attempting to connect to relational database. while creating R2RProviders.[0m
2025-08-04 08:46:18 - [31mERROR[0m - Failed to start R2R server: Error [WinError 121] �źŵƳ�ʱʱ���ѵ� occurred while attempting to connect to relational database.[0m
2025-08-04 08:48:49 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-04 08:48:49 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=nan rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-04 08:48:49 - [33mWARNING[0m - Aliyun embedding model detected, dropping params[0m
2025-08-04 08:48:49 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='litellm' base_model='openai/aliyun/text-embedding-v4' base_dimension=nan rerank_model=None rerank_url=None batch_size=50 concurrent_request_limit=50 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-08-04 08:48:49 - [33mWARNING[0m - Aliyun embedding model detected, dropping params[0m
2025-08-04 08:48:49 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:48:49 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-08-04 08:48:49 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:48:58 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:49:01 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:49:01 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-08-04 08:49:01 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-08-04 08:49:01 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-08-04 08:49:01 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-08-04 08:49:01 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-08-04 08:49:01 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-08-04 08:49:02 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-08-04 08:49:02 - [32mINFO[0m - Creating table, if it does not exist: "cscs_rag"."documents"[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: system[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-08-04 08:49:04 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-08-04 08:49:05 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-08-04 08:49:06 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-08-04 08:49:06 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-08-04 08:49:06 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-08-04 08:49:06 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-08-04 08:49:06 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat-0729\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-08-04 08:49:09 - [32mINFO[0m - Using existing S3 bucket: cscsrag[0m
2025-08-04 08:49:09 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-08-04 08:49:09 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-08-04 08:49:11 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-08-04 08:49:11 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-08-04 08:49:11 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-08-04 08:49:11 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-08-04 08:49:11 - [32mINFO[0m - Default admin user already exists.[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing database maintenance service[0m
2025-08-04 08:49:12 - [32mINFO[0m - Scheduler started[0m
2025-08-04 08:49:12 - [32mINFO[0m - Scheduler started[0m
2025-08-04 08:49:12 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-08-04 08:49:12 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing GraphRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing SystemRouter[0m
2025-08-04 08:49:12 - [32mINFO[0m - Initializing UsersRouter[0m
2025-08-04 08:49:13 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-08-04 08:49:13 - [32mINFO[0m - Started server process [12520][0m
2025-08-04 08:49:13 - [32mINFO[0m - Waiting for application startup.[0m
2025-08-04 08:49:13 - [32mINFO[0m - Application startup complete.[0m
2025-08-04 08:49:13 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-08-04 08:49:57 - [32mINFO[0m - 127.0.0.1:54033 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-04 08:49:57 - [32mINFO[0m - 127.0.0.1:54033 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-08-04 08:49:58 - [32mINFO[0m - 127.0.0.1:54033 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-04 08:49:58 - [32mINFO[0m - 127.0.0.1:54033 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-08-04 08:50:20 - [32mINFO[0m - 127.0.0.1:54039 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-04 08:50:21 - [32mINFO[0m - 127.0.0.1:54039 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-08-04 08:54:58 - [32mINFO[0m - 127.0.0.1:55148 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 08:54:58 - [32mINFO[0m - 127.0.0.1:55148 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:00:02 - [32mINFO[0m - 127.0.0.1:55684 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:05:03 - [32mINFO[0m - 127.0.0.1:56368 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:05:03 - [32mINFO[0m - 127.0.0.1:56368 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:10:04 - [32mINFO[0m - 127.0.0.1:57001 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:15:05 - [32mINFO[0m - 127.0.0.1:57404 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:15:05 - [32mINFO[0m - 127.0.0.1:57404 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:20:06 - [32mINFO[0m - 127.0.0.1:57900 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:25:07 - [32mINFO[0m - 127.0.0.1:58329 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-08-04 09:25:07 - [32mINFO[0m - 127.0.0.1:58329 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
